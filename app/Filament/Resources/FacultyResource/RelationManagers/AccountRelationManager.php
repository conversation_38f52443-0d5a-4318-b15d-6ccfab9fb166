<?php

declare(strict_types=1);

namespace App\Filament\Resources\FacultyResource\RelationManagers;

use App\Models\Account;
use App\Models\Faculty;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class AccountRelationManager extends RelationManager
{
    protected static string $relationship = 'account';

    protected static ?string $title = 'Faculty Account';

    protected static ?string $recordTitleAttribute = 'email';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Account Information')
                    ->schema([
                        TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->default(fn () => $this->getOwnerRecord()->email)
                            ->disabled(),
                        
                        TextInput::make('username')
                            ->required()
                            ->maxLength(255)
                            ->default(fn () => $this->getOwnerRecord()->email),
                        
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->default(fn () => $this->getOwnerRecord()->full_name),
                        
                        Select::make('role')
                            ->options([
                                'faculty' => 'Faculty',
                                'admin' => 'Admin',
                            ])
                            ->default('faculty')
                            ->required(),
                        
                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])->columns(2),
                
                Section::make('Security')
                    ->schema([
                        TextInput::make('password')
                            ->password()
                            ->dehydrateStateUsing(fn ($state) => $state ? Hash::make($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $context): bool => $context === 'create')
                            ->maxLength(255)
                            ->hint('Leave blank to keep unchanged')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('email')
            ->columns([
                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->copyable(),
                
                TextColumn::make('username')
                    ->label('Username')
                    ->searchable(),
                
                TextColumn::make('role')
                    ->label('Role')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'admin' => 'danger',
                        'faculty' => 'success',
                        default => 'gray',
                    }),
                
                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                
                TextColumn::make('last_login')
                    ->label('Last Login')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never'),
                
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->options([
                        'faculty' => 'Faculty',
                        'admin' => 'Admin',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->headerActions([
                Action::make('createAccount')
                    ->label('Create Account')
                    ->icon('heroicon-o-plus')
                    ->color('success')
                    ->visible(fn () => !$this->getOwnerRecord()->hasAccount())
                    ->form([
                        Section::make('Account Details')
                            ->schema([
                                TextInput::make('username')
                                    ->required()
                                    ->maxLength(255)
                                    ->default(fn () => $this->getOwnerRecord()->email)
                                    ->unique(Account::class, 'username'),
                                
                                TextInput::make('password')
                                    ->password()
                                    ->required()
                                    ->maxLength(255)
                                    ->default(fn () => Str::random(12))
                                    ->hint('Auto-generated password. Faculty can change this later.'),
                                
                                Select::make('role')
                                    ->options([
                                        'faculty' => 'Faculty',
                                        'admin' => 'Admin',
                                    ])
                                    ->default('faculty')
                                    ->required(),
                                
                                Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),
                            ])->columns(2),
                    ])
                    ->action(function (array $data): void {
                        $faculty = $this->getOwnerRecord();
                        
                        $account = $faculty->createOrLinkAccount([
                            'username' => $data['username'],
                            'password' => Hash::make($data['password']),
                            'role' => $data['role'],
                            'is_active' => $data['is_active'],
                        ]);

                        Notification::make()
                            ->title('Account Created Successfully')
                            ->body("Account created for {$faculty->full_name}")
                            ->success()
                            ->send();
                    }),
                
                Action::make('linkExistingAccount')
                    ->label('Link Existing Account')
                    ->icon('heroicon-o-link')
                    ->color('info')
                    ->visible(fn () => !$this->getOwnerRecord()->hasAccount())
                    ->form([
                        Select::make('account_id')
                            ->label('Select Account')
                            ->searchable()
                            ->options(function () {
                                return Account::whereNull('person_type')
                                    ->orWhere('person_type', '!=', Faculty::class)
                                    ->pluck('email', 'id');
                            })
                            ->required()
                            ->hint('Only showing accounts not linked to other faculty'),
                    ])
                    ->action(function (array $data): void {
                        $faculty = $this->getOwnerRecord();
                        $account = Account::find($data['account_id']);
                        
                        if ($account) {
                            $account->linkToFaculty($faculty);
                            
                            Notification::make()
                                ->title('Account Linked Successfully')
                                ->body("Account {$account->email} linked to {$faculty->full_name}")
                                ->success()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                
                Action::make('resetPassword')
                    ->label('Reset Password')
                    ->icon('heroicon-o-key')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->action(function ($record): void {
                        $newPassword = Str::random(12);
                        $record->update([
                            'password' => Hash::make($newPassword),
                        ]);

                        Notification::make()
                            ->title('Password Reset')
                            ->body("New password: {$newPassword}")
                            ->warning()
                            ->persistent()
                            ->send();
                    }),
                
                Action::make('unlinkAccount')
                    ->label('Unlink Account')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Unlink Faculty Account')
                    ->modalDescription('This will remove the link between this faculty and their account. The account will remain but won\'t be associated with this faculty.')
                    ->action(function ($record): void {
                        $record->update([
                            'person_type' => null,
                            'role' => 'guest',
                        ]);

                        Notification::make()
                            ->title('Account Unlinked')
                            ->body('Faculty account has been unlinked successfully')
                            ->success()
                            ->send();
                    }),
                
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading('Delete Faculty Account')
                    ->modalDescription('This will permanently delete the account. This action cannot be undone.'),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                return $query->where('email', $this->getOwnerRecord()->email)
                    ->where('person_type', Faculty::class);
            });
    }
}
