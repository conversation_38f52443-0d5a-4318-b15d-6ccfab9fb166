<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\FacultyResource\Pages;
use App\Filament\Resources\FacultyResource\RelationManagers\AccountRelationManager;
use App\Filament\Resources\FacultyResource\RelationManagers\ClassesRelationManager;
use App\Models\Classes;
use App\Models\Faculty;
use App\Services\GeneralSettingsService;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class FacultyResource extends Resource
{
    protected static ?string $model = Faculty::class;

    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make('Faculty Identification')
                ->description('Faculty identification and basic information.')
                ->schema([
                    TextInput::make('faculty_id_number')
                        ->label('Faculty ID Number')
                        ->required()
                        ->unique(ignoreRecord: true)
                        ->maxLength(255)
                        ->placeholder('e.g., 10000001')
                        ->hint('Unique identifier for the faculty member'),

                    TextInput::make('first_name')
                        ->required()
                        ->maxLength(255),

                    TextInput::make('last_name')
                        ->required()
                        ->maxLength(255),

                    TextInput::make('middle_name')
                        ->maxLength(255),

                    Select::make('gender')
                        ->options([
                            'male' => 'Male',
                            'female' => 'Female',
                            'other' => 'Other',
                        ])
                        ->placeholder('Select Gender')
                        ->required(),

                    DatePicker::make('birth_date')
                        ->label('Date of Birth')
                        ->maxDate(now()->subYears(18))
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set) {
                            if ($state) {
                                $age = now()->diffInYears($state);
                                $set('age', $age);
                            }
                        }),

                    TextInput::make('age')
                        ->numeric()
                        ->disabled()
                        ->dehydrated(false)
                        ->hint('Automatically calculated from birth date'),

                ])->columns(3),

            Section::make('Contact Details')
                ->schema([
                    TextInput::make('email')
                        ->email()
                        ->required()
                        ->unique(ignoreRecord: true)
                        ->maxLength(255),
                    TextInput::make('phone_number')
                        ->tel()
                        ->maxLength(255),
                    TextInput::make('address_line1')
                        ->maxLength(255),
                ])->columns(3),
            Section::make('Professional Details')
                ->description('Academic and professional information.')
                ->schema([
                    Select::make('department')
                        ->options([
                            'Computer Science' => 'Computer Science',
                            'Information Technology' => 'Information Technology',
                            'Engineering' => 'Engineering',
                            'Business Administration' => 'Business Administration',
                            'Education' => 'Education',
                            'Liberal Arts' => 'Liberal Arts',
                            'Sciences' => 'Sciences',
                            'Mathematics' => 'Mathematics',
                            'English' => 'English',
                            'Filipino' => 'Filipino',
                            'Social Sciences' => 'Social Sciences',
                            'Physical Education' => 'Physical Education',
                            'NSTP' => 'NSTP',
                        ])
                        ->searchable()
                        ->placeholder('Select Department')
                        ->required(),

                    TextInput::make('office_hours')
                        ->maxLength(255)
                        ->placeholder('e.g., MWF 9:00-11:00 AM')
                        ->hint('Specify days and times when faculty is available'),

                    Select::make('status')
                        ->options([
                            'active' => 'Active',
                            'inactive' => 'Inactive',
                            'on_leave' => 'On Leave',
                            'retired' => 'Retired',
                        ])
                        ->default('active')
                        ->required()
                        ->placeholder('Select Status'),
                ])->columns(3),

            Section::make('Additional Information')
                ->schema([
                    Textarea::make('biography')
                        ->columnSpanFull(),
                    Textarea::make('education')
                        ->columnSpanFull(),
                    Textarea::make('courses_taught')
                        ->columnSpanFull(),
                ]),
            Section::make('Avatar')
                ->schema([
                    FileUpload::make('photo_url')
                        ->label('Profile Picture')
                        ->image()
                        ->columnSpanFull(),
                ]),

            Section::make('Account Information')
                ->description('Account management is handled separately through the Account tab.')
                ->schema([
                    TextInput::make('account_status')
                        ->label('Account Status')
                        ->disabled()
                        ->dehydrated(false)
                        ->default(fn ($record) => $record ? $record->account_status : 'No Account')
                        ->hint('Create or manage account through the Account relation tab'),
                ])->columns(1)
                ->visible(fn ($context) => $context === 'edit'),

        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('photo_url')
                    ->label('Avatar')
                    ->circular()
                    ->size(40),

                TextColumn::make('faculty_id_number')
                    ->label('Faculty ID')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold'),

                TextColumn::make('full_name')
                    ->label('Full Name')
                    ->searchable(['first_name', 'last_name', 'middle_name'])
                    ->sortable(['last_name', 'first_name'])
                    ->weight('medium'),

                TextColumn::make('email')
                    ->searchable()
                    ->copyable()
                    ->icon('heroicon-m-envelope'),

                TextColumn::make('department')
                    ->searchable()
                    ->badge()
                    ->color('info'),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                        'on_leave' => 'warning',
                        'retired' => 'danger',
                        default => 'gray',
                    }),

                TextColumn::make('account_status')
                    ->label('Account')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Active' => 'success',
                        'Inactive' => 'warning',
                        'No Account' => 'gray',
                        default => 'gray',
                    }),
                TextColumn::make('phone_number')
                    ->label('Phone')
                    ->icon('heroicon-m-phone')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('office_hours')
                    ->label('Office Hours')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->wrap(),

                TextColumn::make('gender')
                    ->badge()
                    ->color('gray')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('age')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('birth_date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('address_line1')
                    ->label('Address')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->wrap(),
                TextColumn::make('current_classes_count')
                    ->label('Current Classes')
                    ->getStateUsing(function ($record) {
                        $settingsService = app(GeneralSettingsService::class);
                        $schoolYearWithSpaces = $settingsService->getCurrentSchoolYearString();
                        $schoolYearNoSpaces = str_replace(' ', '', $schoolYearWithSpaces);
                        $semester = $settingsService->getCurrentSemester();

                        return Classes::whereRaw('faculty_id::text = ?', [$record->id])
                            ->whereIn('school_year', [$schoolYearWithSpaces, $schoolYearNoSpaces])
                            ->where('semester', $semester)
                            ->count();
                    })
                    ->badge()
                    ->color(fn ($state) => $state > 0 ? 'success' : 'gray')
                    ->icon('heroicon-m-academic-cap'),

                TextColumn::make('total_classes_count')
                    ->label('Total Classes')
                    ->getStateUsing(fn ($record) => $record->classes()->count())
                    ->badge()
                    ->color('info')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'on_leave' => 'On Leave',
                        'retired' => 'Retired',
                    ]),

                Tables\Filters\SelectFilter::make('department')
                    ->options([
                        'Computer Science' => 'Computer Science',
                        'Information Technology' => 'Information Technology',
                        'Engineering' => 'Engineering',
                        'Business Administration' => 'Business Administration',
                        'Education' => 'Education',
                        'Liberal Arts' => 'Liberal Arts',
                        'Sciences' => 'Sciences',
                        'Mathematics' => 'Mathematics',
                        'English' => 'English',
                        'Filipino' => 'Filipino',
                        'Social Sciences' => 'Social Sciences',
                        'Physical Education' => 'Physical Education',
                        'NSTP' => 'NSTP',
                    ]),

                Tables\Filters\SelectFilter::make('gender')
                    ->options([
                        'male' => 'Male',
                        'female' => 'Female',
                        'other' => 'Other',
                    ]),

                Tables\Filters\Filter::make('has_account')
                    ->label('Has Account')
                    ->query(fn (Builder $query): Builder =>
                        $query->whereHas('account')
                    ),

                Tables\Filters\Filter::make('no_account')
                    ->label('No Account')
                    ->query(fn (Builder $query): Builder =>
                        $query->whereDoesntHave('account')
                    ),
                Tables\Filters\Filter::make('has_current_classes')
                    ->label('Has Current Classes')
                    ->query(function (Builder $query): Builder {
                        $settingsService = app(GeneralSettingsService::class);
                        $schoolYearWithSpaces = $settingsService->getCurrentSchoolYearString();
                        $schoolYearNoSpaces = str_replace(' ', '', $schoolYearWithSpaces);
                        $semester = $settingsService->getCurrentSemester();

                        return $query->whereExists(function ($subQuery) use ($schoolYearWithSpaces, $schoolYearNoSpaces, $semester): void {
                            $subQuery->select(DB::raw(1))
                                ->from('classes')
                                ->whereRaw('classes.faculty_id::text = faculty.id::text')
                                ->whereIn('classes.school_year', [$schoolYearWithSpaces, $schoolYearNoSpaces])
                                ->where('classes.semester', $semester);
                        });
                    }),
                Tables\Filters\Filter::make('no_current_classes')
                    ->label('No Current Classes')
                    ->query(function (Builder $query): Builder {
                        $settingsService = app(GeneralSettingsService::class);
                        $schoolYearWithSpaces = $settingsService->getCurrentSchoolYearString();
                        $schoolYearNoSpaces = str_replace(' ', '', $schoolYearWithSpaces);
                        $semester = $settingsService->getCurrentSemester();

                        return $query->whereNotExists(function ($subQuery) use ($schoolYearWithSpaces, $schoolYearNoSpaces, $semester): void {
                            $subQuery->select(DB::raw(1))
                                ->from('classes')
                                ->whereRaw('classes.faculty_id::text = faculty.id::text')
                                ->whereIn('classes.school_year', [$schoolYearWithSpaces, $schoolYearNoSpaces])
                                ->where('classes.semester', $semester);
                        });
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                Action::make('manageAccount')
                    ->label('Account')
                    ->icon('heroicon-o-user-circle')
                    ->color(fn (Faculty $record) => $record->hasAccount() ? 'success' : 'warning')
                    ->button()
                    ->visible(fn (Faculty $record) => !$record->hasAccount())
                    ->form([
                        Section::make('Create Faculty Account')
                            ->schema([
                                TextInput::make('username')
                                    ->required()
                                    ->maxLength(255)
                                    ->default(fn (Faculty $record) => $record->email)
                                    ->unique('accounts', 'username'),

                                TextInput::make('password')
                                    ->password()
                                    ->required()
                                    ->maxLength(255)
                                    ->default(fn () => Str::random(12))
                                    ->hint('Auto-generated password. Faculty can change this later.'),

                                Select::make('role')
                                    ->options([
                                        'faculty' => 'Faculty',
                                        'admin' => 'Admin',
                                    ])
                                    ->default('faculty')
                                    ->required(),

                                Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),
                            ])->columns(2),
                    ])
                    ->action(function (array $data, Faculty $record): void {
                        $record->createOrLinkAccount([
                            'username' => $data['username'],
                            'password' => Hash::make($data['password']),
                            'role' => $data['role'],
                            'is_active' => $data['is_active'],
                        ]);

                        Notification::make()
                            ->title('Account Created Successfully')
                            ->body("Account created for {$record->full_name} with password: {$data['password']}")
                            ->success()
                            ->persistent()
                            ->send();
                    }),
                Action::make('assignClasses')
                    ->label('Assign Classes')
                    ->icon('heroicon-o-academic-cap')
                    ->color('info')
                    ->form([
                        Select::make('class_ids')
                            ->label('Select Classes to Assign')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                return Classes::currentAcademicPeriod()
                                    ->whereNull('faculty_id')
                                    ->get()
                                    ->mapWithKeys(function ($class) {
                                        $type = $class->isShs() ? 'SHS' : 'College';
                                        $info = $class->isShs() ? $class->formatted_track_strand : $class->formatted_course_codes;
                                        $label = "{$class->subject_title} - {$class->section} ({$type})";
                                        if ($info && $info !== 'N/A') {
                                            $label .= " - {$info}";
                                        }

                                        return [$class->id => $label];
                                    });
                            })
                            ->hint('Only showing unassigned classes for current academic period'),
                    ])
                    ->action(function (array $data, Faculty $record): void {
                        if (! empty($data['class_ids'])) {
                            Classes::whereIn('id', $data['class_ids'])
                                ->update(['faculty_id' => (string) $record->id]);

                            $count = count($data['class_ids']);
                            Notification::make()
                                ->title('Classes Assigned Successfully')
                                ->body("Assigned {$count} class(es) to {$record->full_name}")
                                ->success()
                                ->send();
                        }
                    }),
                Action::make('resetPassword')
                    ->label('Reset Password')
                    ->icon('heroicon-o-key')
                    ->color('warning')
                    ->visible(fn (Faculty $record) => $record->hasAccount())
                    ->requiresConfirmation()
                    ->modalHeading('Reset Faculty Password')
                    ->modalDescription('This will generate a new password for the faculty member.')
                    ->action(function (Faculty $record): void {
                        if ($record->account) {
                            $newPassword = Str::random(12);
                            $record->account->update([
                                'password' => Hash::make($newPassword),
                            ]);

                            Notification::make()
                                ->title('Password Reset Successfully')
                                ->body("New password for {$record->full_name}: {$newPassword}")
                                ->warning()
                                ->persistent()
                                ->send();
                        }
                    }),

                Action::make('toggleAccountStatus')
                    ->label(fn (Faculty $record) => $record->hasAccount() && $record->account->is_active ? 'Deactivate Account' : 'Activate Account')
                    ->icon(fn (Faculty $record) => $record->hasAccount() && $record->account->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (Faculty $record) => $record->hasAccount() && $record->account->is_active ? 'danger' : 'success')
                    ->visible(fn (Faculty $record) => $record->hasAccount())
                    ->requiresConfirmation()
                    ->action(function (Faculty $record): void {
                        if ($record->account) {
                            $newStatus = !$record->account->is_active;
                            $record->account->update(['is_active' => $newStatus]);

                            Notification::make()
                                ->title('Account Status Updated')
                                ->body("Account for {$record->full_name} has been " . ($newStatus ? 'activated' : 'deactivated'))
                                ->success()
                                ->send();
                        }
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Action::make('bulkAssignClasses')
                        ->label('Assign Classes to Selected Faculty')
                        ->icon('heroicon-o-academic-cap')
                        ->color('info')
                        ->form([
                            Select::make('class_assignments')
                                ->label('Class Assignments')
                                ->multiple()
                                ->searchable()
                                ->preload()
                                ->options(function () {
                                    return Classes::currentAcademicPeriod()
                                        ->whereNull('faculty_id')
                                        ->get()
                                        ->mapWithKeys(function ($class) {
                                            $type = $class->isShs() ? 'SHS' : 'College';
                                            $info = $class->isShs() ? $class->formatted_track_strand : $class->formatted_course_codes;
                                            $label = "{$class->subject_title} - {$class->section} ({$type})";
                                            if ($info && $info !== 'N/A') {
                                                $label .= " - {$info}";
                                            }

                                            return [$class->id => $label];
                                        });
                                })
                                ->hint('Select classes to distribute among selected faculty members'),
                        ])
                        ->action(function (array $data, $records): void {
                            if (! empty($data['class_assignments'])) {
                                $facultyMembers = $records->toArray();
                                $classIds = $data['class_assignments'];
                                $facultyCount = count($facultyMembers);

                                if ($facultyCount > 0) {
                                    // Distribute classes evenly among selected faculty
                                    foreach ($classIds as $index => $classId) {
                                        $facultyIndex = $index % $facultyCount;
                                        $facultyId = (string) $facultyMembers[$facultyIndex]['id'];

                                        Classes::where('id', $classId)
                                            ->update(['faculty_id' => $facultyId]);
                                    }

                                    $classCount = count($classIds);
                                    Notification::make()
                                        ->title('Classes Assigned Successfully')
                                        ->body("Distributed {$classCount} class(es) among {$facultyCount} faculty member(s)")
                                        ->success()
                                        ->send();
                                }
                            }
                        }),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            AccountRelationManager::class,
            ClassesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'dashboard' => Pages\FacultyDashboard::route('/dashboard'),
            'index' => Pages\ListFaculties::route('/'),
            'create' => Pages\CreateFaculty::route('/create'),
            'edit' => Pages\EditFaculty::route('/{record}/edit'),
            'view' => Pages\ViewFaculty::route('/{record}'),
            'manage-assignments' => Pages\ManageClassAssignments::route('/manage-assignments'),
        ];
    }
}
