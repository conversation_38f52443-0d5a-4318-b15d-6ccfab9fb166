<?php

declare(strict_types=1);

namespace App\Filament\Resources\FacultyResource\Pages;

use App\Filament\Resources\FacultyResource;
use App\Models\Classes;
use App\Models\Faculty;
use App\Services\GeneralSettingsService;
use Filament\Actions\Action;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class FacultyDashboard extends Page
{
    protected static string $resource = FacultyResource::class;

    protected static string $view = 'filament.resources.faculty-resource.pages.faculty-dashboard';

    protected static ?string $title = 'Faculty Management Dashboard';

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?string $slug = 'dashboard';

    protected static ?int $navigationSort = 1;

    public function getHeaderActions(): array
    {
        return [
            Action::make('createFaculty')
                ->label('Add New Faculty')
                ->icon('heroicon-o-plus')
                ->color('success')
                ->url(route('filament.admin.resources.faculty.create')),
            
            Action::make('manageAssignments')
                ->label('Manage Class Assignments')
                ->icon('heroicon-o-academic-cap')
                ->color('info')
                ->url(route('filament.admin.resources.faculty.manage-assignments')),
            
            Action::make('bulkCreateAccounts')
                ->label('Bulk Create Accounts')
                ->icon('heroicon-o-user-plus')
                ->color('warning')
                ->form([
                    Section::make('Bulk Account Creation')
                        ->description('Create accounts for faculty members who don\'t have one yet.')
                        ->schema([
                            Select::make('faculty_ids')
                                ->label('Select Faculty Members')
                                ->multiple()
                                ->searchable()
                                ->options(function () {
                                    return Faculty::whereDoesntHave('account')
                                        ->get()
                                        ->pluck('full_name', 'id');
                                })
                                ->required()
                                ->hint('Only showing faculty without accounts'),
                            
                            Select::make('default_role')
                                ->label('Default Role')
                                ->options([
                                    'faculty' => 'Faculty',
                                    'admin' => 'Admin',
                                ])
                                ->default('faculty')
                                ->required(),
                            
                            Toggle::make('is_active')
                                ->label('Active by Default')
                                ->default(true),
                        ]),
                ])
                ->action(function (array $data): void {
                    $facultyMembers = Faculty::whereIn('id', $data['faculty_ids'])->get();
                    $created = 0;
                    $passwords = [];

                    foreach ($facultyMembers as $faculty) {
                        $password = Str::random(12);
                        $faculty->createOrLinkAccount([
                            'username' => $faculty->email,
                            'password' => Hash::make($password),
                            'role' => $data['default_role'],
                            'is_active' => $data['is_active'],
                        ]);
                        
                        $passwords[] = "{$faculty->full_name}: {$password}";
                        $created++;
                    }

                    Notification::make()
                        ->title('Accounts Created Successfully')
                        ->body("Created {$created} accounts. Passwords: " . implode(', ', $passwords))
                        ->success()
                        ->persistent()
                        ->send();
                }),
        ];
    }

    public function getHeaderWidgets(): array
    {
        return [
            FacultyStatsWidget::class,
        ];
    }
}

final class FacultyStatsWidget extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();

        // Total faculty count
        $totalFaculty = Faculty::count();

        // Active faculty count
        $activeFaculty = Faculty::where('status', 'active')->count();

        // Faculty with accounts
        $facultyWithAccounts = Faculty::whereHas('account')->count();

        // Faculty without accounts
        $facultyWithoutAccounts = $totalFaculty - $facultyWithAccounts;

        // Current classes count
        $currentClasses = Classes::whereIn('school_year', [$currentSchoolYear, str_replace(' ', '', $currentSchoolYear)])
            ->where('semester', $currentSemester)
            ->whereNotNull('faculty_id')
            ->count();

        // Unassigned classes count
        $unassignedClasses = Classes::whereIn('school_year', [$currentSchoolYear, str_replace(' ', '', $currentSchoolYear)])
            ->where('semester', $currentSemester)
            ->whereNull('faculty_id')
            ->count();

        // Faculty by department
        $departmentCounts = Faculty::where('status', 'active')
            ->groupBy('department')
            ->selectRaw('department, count(*) as count')
            ->pluck('count', 'department')
            ->toArray();

        $topDepartment = !empty($departmentCounts) ? array_keys($departmentCounts, max($departmentCounts))[0] : 'N/A';

        return [
            Stat::make('Total Faculty', $totalFaculty)
                ->description('All faculty members')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Active Faculty', $activeFaculty)
                ->description('Currently active')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('With Accounts', $facultyWithAccounts)
                ->description('Have login accounts')
                ->descriptionIcon('heroicon-m-user-circle')
                ->color('info'),

            Stat::make('Without Accounts', $facultyWithoutAccounts)
                ->description('Need account creation')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($facultyWithoutAccounts > 0 ? 'warning' : 'success'),

            Stat::make('Assigned Classes', $currentClasses)
                ->description("Current semester ({$currentSchoolYear})")
                ->descriptionIcon('heroicon-m-academic-cap')
                ->color('success'),

            Stat::make('Unassigned Classes', $unassignedClasses)
                ->description('Need faculty assignment')
                ->descriptionIcon('heroicon-m-exclamation-circle')
                ->color($unassignedClasses > 0 ? 'danger' : 'success'),

            Stat::make('Top Department', $topDepartment)
                ->description('Most faculty members')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('gray'),

            Stat::make('Departments', count($departmentCounts))
                ->description('Total departments')
                ->descriptionIcon('heroicon-m-squares-2x2')
                ->color('gray'),
        ];
    }
}
