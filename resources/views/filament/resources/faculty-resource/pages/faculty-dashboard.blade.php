<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            @foreach ($this->getHeaderWidgets() as $widget)
                @livewire($widget)
            @endforeach
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Faculty Management -->
            <x-filament::section>
                <x-slot name="heading">
                    Faculty Management
                </x-slot>
                
                <div class="space-y-3">
                    <x-filament::button
                        wire:click="$dispatch('open-modal', { id: 'create-faculty' })"
                        color="success"
                        icon="heroicon-o-plus"
                        size="sm"
                        class="w-full"
                    >
                        Add New Faculty
                    </x-filament::button>
                    
                    <x-filament::button
                        href="{{ route('filament.admin.resources.faculty.index') }}"
                        color="primary"
                        icon="heroicon-o-users"
                        size="sm"
                        class="w-full"
                    >
                        View All Faculty
                    </x-filament::button>
                    
                    <x-filament::button
                        href="{{ route('filament.admin.resources.faculty.index', ['tableFilters[no_account][value]' => true]) }}"
                        color="warning"
                        icon="heroicon-o-exclamation-triangle"
                        size="sm"
                        class="w-full"
                    >
                        Faculty Without Accounts
                    </x-filament::button>
                </div>
            </x-filament::section>

            <!-- Class Management -->
            <x-filament::section>
                <x-slot name="heading">
                    Class Management
                </x-slot>
                
                <div class="space-y-3">
                    <x-filament::button
                        href="{{ route('filament.admin.resources.faculty.manage-assignments') }}"
                        color="info"
                        icon="heroicon-o-academic-cap"
                        size="sm"
                        class="w-full"
                    >
                        Manage Assignments
                    </x-filament::button>
                    
                    <x-filament::button
                        href="{{ route('filament.admin.resources.classes.index', ['tableFilters[faculty_assigned][value]' => false]) }}"
                        color="danger"
                        icon="heroicon-o-exclamation-circle"
                        size="sm"
                        class="w-full"
                    >
                        Unassigned Classes
                    </x-filament::button>
                    
                    <x-filament::button
                        href="{{ route('filament.admin.resources.faculty.index', ['tableFilters[has_current_classes][value]' => true]) }}"
                        color="success"
                        icon="heroicon-o-check-circle"
                        size="sm"
                        class="w-full"
                    >
                        Faculty with Classes
                    </x-filament::button>
                </div>
            </x-filament::section>

            <!-- Account Management -->
            <x-filament::section>
                <x-slot name="heading">
                    Account Management
                </x-slot>
                
                <div class="space-y-3">
                    <x-filament::button
                        wire:click="$dispatch('open-modal', { id: 'bulk-create-accounts' })"
                        color="warning"
                        icon="heroicon-o-user-plus"
                        size="sm"
                        class="w-full"
                    >
                        Bulk Create Accounts
                    </x-filament::button>
                    
                    <x-filament::button
                        href="{{ route('filament.admin.resources.accounts.index', ['tableFilters[role][value]' => 'faculty']) }}"
                        color="primary"
                        icon="heroicon-o-user-circle"
                        size="sm"
                        class="w-full"
                    >
                        View Faculty Accounts
                    </x-filament::button>
                    
                    <x-filament::button
                        href="{{ route('filament.admin.resources.faculty.index', ['tableFilters[has_account][value]' => true]) }}"
                        color="success"
                        icon="heroicon-o-check-badge"
                        size="sm"
                        class="w-full"
                    >
                        Faculty with Accounts
                    </x-filament::button>
                </div>
            </x-filament::section>
        </div>

        <!-- Recent Activity -->
        <x-filament::section>
            <x-slot name="heading">
                Recent Faculty Activity
            </x-slot>
            
            <div class="space-y-4">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p>• Recently added faculty members will appear here</p>
                    <p>• Recent class assignments will be tracked</p>
                    <p>• Account creation activities will be logged</p>
                    <p>• Faculty status changes will be monitored</p>
                </div>
                
                <x-filament::button
                    href="{{ route('filament.admin.resources.faculty.index', ['tableSortColumn' => 'created_at', 'tableSortDirection' => 'desc']) }}"
                    color="gray"
                    icon="heroicon-o-clock"
                    size="sm"
                >
                    View Recent Faculty
                </x-filament::button>
            </div>
        </x-filament::section>

        <!-- Department Overview -->
        <x-filament::section>
            <x-slot name="heading">
                Department Overview
            </x-slot>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                @php
                    $departments = [
                        'Computer Science' => 'heroicon-o-computer-desktop',
                        'Information Technology' => 'heroicon-o-cpu-chip',
                        'Engineering' => 'heroicon-o-cog-6-tooth',
                        'Business Administration' => 'heroicon-o-briefcase',
                        'Education' => 'heroicon-o-academic-cap',
                        'Liberal Arts' => 'heroicon-o-book-open',
                        'Sciences' => 'heroicon-o-beaker',
                        'Mathematics' => 'heroicon-o-calculator',
                    ];
                @endphp
                
                @foreach($departments as $dept => $icon)
                    <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <x-heroicon-o-users class="w-5 h-5 text-gray-500" />
                            <span class="text-sm font-medium">{{ $dept }}</span>
                        </div>
                        <div class="mt-1 text-xs text-gray-600 dark:text-gray-400">
                            <a href="{{ route('filament.admin.resources.faculty.index', ['tableFilters[department][value]' => $dept]) }}" 
                               class="hover:text-primary-600">
                                View Faculty
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
